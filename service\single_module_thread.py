from PySide6.QtCore import QThread
from pandas import DataFrame, ExcelWriter
from openpyxl import load_workbook, Workbook
from openpyxl.styles import Font, Alignment, PatternFill

from typing import TYPE_CHECKING, List, Tuple
from datetime import datetime, timedelta
import os
import traceback
import math

from front.core.signals import signals
from service.common.thread_logger_adapter import ThreadLoggerAdapter
from service.common.my_pcs import MyPcs
from service.common.const import (
    PROCESS_NAME,
    EXCEL_DATETIME_FORMAT,
    EXCEL_NAME_TIME_FORMAT,
    BASE_OUTPUT_FILE_PATH
)
from service.common.save_condition import save_config_list, save_key_vdm_address_key_map
from service.common.parse import (
    signed_num,
    signed_f16_hex_2_float,
    power_calc,
    hex_str_to_float,
    uint8_4_to_float
)
from service.common.excel import auto_adjust_column_width



class SingleModuleThread(QThread):
    def __init__(
        self,
        ip: str,
        port: int,
        slot: int,
        sn: str,
        module,
        test_start_time: datetime,
        duration_time: float,
        single_loop_time: int,
        logger: ThreadLoggerAdapter = None,
    ):
        super().__init__()
        self.ip = ip
        self.port = port
        self.slot = slot
        self.sn = sn
        self.unique_key = (self.ip, self.port, self.slot)
        self.mn = ""
        self.module = module
        self.test_start_time = test_start_time
        self.duration_time = duration_time
        self.single_loop_time = single_loop_time
        self.logger = logger
        self.control_data_df = DataFrame()
        self.control_data = []
        self.vertical_control_data = []
        self.vertical_control_data_df = DataFrame()

    def run(self):
        # 先通过MES获取MN
        try:
            my_pcs = MyPcs()
            my_pcs.set_logger(self.logger)
            mn = my_pcs.get_mn(f"AL{self.sn}")
            # 将mn通过信号发送到UI界面
            signals.table_update_single_row_signal.emit(self.unique_key, {"mn": mn})
            my_pcs.start_process(mn, PROCESS_NAME)
            # todo
            # 测试完成后上传MES数据
            # 正式挂机前确保freeze/unfreeze一次，保证清除之前的误码状态
            self.logger.info("开始冻结")
            self.module.qdd_freeze_request(1, self.port)
            self.logger.info("冻结完成")
            self.logger.info("开始解冻")
            self.module.qdd_freeze_request(0, self.port)
            self.logger.info("解冻完成")
            # 开始读取模块数据
            # todo
            # 多个模块并行测试
            test_start_time_str = self.test_start_time.strftime(EXCEL_NAME_TIME_FORMAT)
            test_end_time = self.test_start_time + timedelta(hours=self.duration_time)
            single_loop_time_min = timedelta(seconds=self.single_loop_time)
            loop_start_time = datetime.now()
            control_file_path = (
                f"{BASE_OUTPUT_FILE_PATH}/{test_start_time_str}__{self.sn}卡控文件.xlsx"
            )
            # 提前读取一次vdm_address,后续才能够通过vdm方式读取vdm值
            vdm_result = self.module.get_vdm_address()
            while loop_start_time <= test_end_time:
                self.read_all_data()
                is_this_time_pass, result_str = self.judge_this_time_pass()
                self.output_control_file(
                    control_file_path,
                    "sheet1",
                    "sheet2",
                    [
                        "卡控指标名称",
                        "读取时间",
                        "min_value",
                        "max_value",
                        "测试结果值",
                        "单位",
                        "是否通过",
                    ],
                    is_this_time_pass,
                    result_str,
                )
                # self.output_vertical_control_file(
                #     control_file_path,
                #     "sheet2",
                #     [],
                #     is_this_time_pass,
                #     result_str,
                # )
                loop_end_time = datetime.now()
                single_loop_time = loop_end_time - loop_start_time
                delta_loop_time = (
                    single_loop_time_min - single_loop_time
                ).total_seconds()
                if delta_loop_time > 0:
                    self.msleep(int(delta_loop_time * 1000))
                loop_start_time = datetime.now()
            my_pcs.upload_data(mn, control_file_path)
            # 实际程序需要正常结束,测试时只上传不结束工序
            my_pcs.end_process(mn, is_this_time_pass)
        except Exception as e:
            self.logger.error(f"{e}")
            traceback.print_exc()
            signals.error_dialog_signal.emit(
                f"ip-{self.ip},port-{self.port},slot-{self.slot},sn-{self.sn}测试终止, {e}"
            )

    def read_all_data(self):
        loop_time_str = datetime.now().strftime(EXCEL_DATETIME_FORMAT)
        vertical_row_dict = {"读取时间": loop_time_str}
        for index, config_dict in enumerate(save_config_list):
            name: str = config_dict["name"]
            show_name: str = config_dict["show_name"]
            page: int = config_dict["page"]
            start_reg: int = config_dict["start_reg"]
            end_reg: int = config_dict["end_reg"]
            is_control_value: bool = config_dict["is_control_value"]
            is_show_value: bool = config_dict["is_show_value"]
            min_value = config_dict["min_value"]
            max_value = config_dict["max_value"]
            zoom_way = config_dict["zoom_way"]
            coefficient = config_dict["coefficient"]
            unit = config_dict["unit"]
            value_type = config_dict["value_type"]
            parent_name = config_dict["parent_name"]
            reg_num = end_reg - start_reg + 1
            if parent_name == "VDM":
                monitored_resource = config_dict["monitored_resource"]
                value_tuple = self.module.vdm_value(
                    save_key_vdm_address_key_map[name], monitored_resource
                )
                if value_tuple != None:
                    value = value_tuple[0]
                else:
                    raise Exception(
                        f"vdmid:{save_key_vdm_address_key_map[name]},monitored_resource:{monitored_resource}无法通过VDM获取值"
                    )
            else:
                if value_type == "string":
                    ori_value = self.module.read_regs_int(0, page, start_reg, reg_num)
                elif value_type == "ori_decimal":
                    ori_value = self.module.read_regs_str(0, page, start_reg, reg_num)
                    value = int(ori_value, 16)
                elif value_type == "ori_hex":
                    ori_value = self.module.read_regs_str(0, page, start_reg, reg_num)
                    value = ori_value
                elif value_type == "gc_optical_pwr":
                    # 四路tx gc使用单独的函数转换
                    ori_value = self.module.read_regs_int(0, page, start_reg, reg_num)
                    value = power_calc(ori_value)
                elif value_type == "F32":
                    # ori_value_str = self.module.read_regs_str(0, page, start_reg, reg_num) 
                    ori_value_int = self.module.read_regs_int(0, page, start_reg, reg_num)
                    value = uint8_4_to_float(ori_value_int)
                else:
                    ori_value = self.module.read_regs_str(0, page, start_reg, reg_num)
                    if value_type == "U16":
                        value = int(ori_value, 16)
                    elif value_type == "S16":
                        value = signed_num(int(ori_value, 16), 16)
                    elif value_type == "F16":
                        value = signed_f16_hex_2_float(ori_value)
                    # elif value_type == "F32":
                    #     value = hex_str_to_float(ori_value)
                if not (zoom_way is None):
                    if zoom_way == "division":
                        value = value / coefficient
                    elif zoom_way == "multiply":
                        value = value * coefficient
                    elif zoom_way == "mW_to_dBm":
                        if value > 0:
                            value = round(10 * math.log10(value), 3)
                        else:
                            value = -40
            single_test_name_is_pass = 0
            if is_control_value:
                if min_value != None:
                    if value < min_value:
                        single_test_name_is_pass = 1
                if max_value != None:
                    if value > max_value:
                        single_test_name_is_pass = 1
            time_str = datetime.now().strftime(EXCEL_DATETIME_FORMAT)
            new_row_dict = {}
            new_row_dict = {
                "测试结果值": value,
                "是否通过": single_test_name_is_pass,
                "读取时间": time_str,
                "单位": unit,
            }
            new_row_dict.update(
                {
                    "卡控指标名称": config_dict["name"],
                    "min_value": config_dict["min_value"],
                    "max_value": config_dict["max_value"],
                }
            )
            self.control_data.append(new_row_dict)
            value_column_name = (
                f'{config_dict["name"]}/{unit}' if unit else config_dict["name"]
            )
            vertical_row_dict.update({f"{value_column_name}": value})
            if is_show_value:
                update_dict = {name: value}
                signals.table_update_single_row_signal.emit(
                    self.unique_key, update_dict
                )
        self.vertical_control_data.append(vertical_row_dict)

    def output_control_file(
        self,
        control_file_path: str,
        sheet_name_1: str,
        sheet_name_2: str,
        control_data_columns: List[str],
        is_this_time_pass: bool,
        result_str: str,
    ):
        """将控制数据输出到 Excel 文件中。

        该方法将控制数据写入指定路径和表单的 Excel 文件中，并在第一行合并单元格显示测试结果信息。

        Args:
            control_file_path (str): 控制文件保存路径。
            sheet_name (str): Excel 表单名称。
            control_data_columns (List[str]): 控制数据的列名称列表。
            is_this_time_pass (bool): 当前测试是否通过。
            result_str (str): 测试结果文本。
        """
        self.control_data_df = DataFrame(self.control_data)
        self.vertical_control_data_df = DataFrame(self.vertical_control_data)
        # if not os.path.exists(os.path.dirname(control_file_path)):
        #     os.makedirs(os.path.dirname(control_file_path))
        if not os.path.exists(control_file_path):
            # 文件不存在时，创建文件
            wb = Workbook()
            ws1 = wb.active
            ws1.title = sheet_name_1
            wb.create_sheet(sheet_name_2)
            wb.save(control_file_path)
        # 使用 ExcelWriter 打开文件，engine 为 openpyxl
        with ExcelWriter(
            control_file_path, engine="openpyxl", mode="a", if_sheet_exists="replace"
        ) as writer:
            # 将 DataFrame 写入 Excel 的指定 Sheet
            self.control_data_df.to_excel(
                writer,
                sheet_name=sheet_name_1,
                startrow=1,
                index=False,
                columns=control_data_columns,
            )
            self.vertical_control_data_df.to_excel(
                writer, sheet_name=sheet_name_2, index=False
            )
            # 获取 workbook 和 worksheet 对象
            workbook: Workbook = writer.book
            worksheet_1 = writer.sheets[sheet_name_1]
            # 自动调整列宽
            worksheet_2 = writer.sheets[sheet_name_2]
            auto_adjust_column_width(worksheet_1)
            auto_adjust_column_width(worksheet_2)
            # 添加合并行
            merge_text = result_str
            num_columns = len(self.control_data_df.columns)  # 数据的列数
            font_color_mapper = {True: "00FF00", False: "FF0000"}
            font_color = font_color_mapper.get(is_this_time_pass, "000000")
            bg_color_mapper = {True: "DFFFD6", False: "FFD6D6"}
            bg_color = bg_color_mapper.get(is_this_time_pass, "FFFFFF")

            worksheet_1.merge_cells(
                start_row=1, start_column=1, end_row=1, end_column=num_columns
            )
            merged_cell = worksheet_1.cell(row=1, column=1)
            merged_cell.value = merge_text
            merged_cell.font = Font(bold=True, color=font_color, size=14)
            merged_cell.alignment = Alignment(horizontal="center", vertical="center")
            merged_cell.fill = PatternFill(
                start_color=bg_color, end_color=bg_color, fill_type="solid"
            )
            # 保存文件
            workbook.save(control_file_path)

    def output_vertical_control_file(
        self,
        control_file_path: str,
        sheet_name: str,
    ):
        self.vertical_control_data_df = DataFrame(self.vertical_control_data)
        self.control_data_df = DataFrame(self.control_data)
        if not os.path.exists(os.path.dirname(control_file_path)):
            os.makedirs(os.path.dirname(control_file_path))
        if not os.path.exists(control_file_path):
            # 文件不存在时，创建文件
            wb = Workbook()
            ws1 = wb.active
            ws1.title = sheet_name
            wb.save(control_file_path)
        # 使用 ExcelWriter 打开文件，engine 为 openpyxl
        with ExcelWriter(
            control_file_path, engine="openpyxl", mode="a", if_sheet_exists="replace"
        ) as writer:
            # 将 DataFrame 写入 Excel 的指定 Sheet
            self.vertical_control_data_df.to_excel(
                writer, sheet_name=sheet_name, index=False
            )

            # 获取 workbook 和 worksheet 对象
            workbook: Workbook = writer.book
            worksheet = writer.sheets[sheet_name]
            # 自动调整列宽
            auto_adjust_column_width(worksheet)
            # 保存文件
            workbook.save(control_file_path)

    def judge_this_time_pass(self) -> Tuple[bool, str]:
        """判断本次测试是否通过。

        该方法根据控制数据中“是否通过”的标志判断本次测试是否全部满足要求，
        并返回测试结果及提示信息。

        Returns:
            Tuple[bool, str]: 测试通过返回 (True, "测试通过")，否则返回 (False, "测试失败!!")。
        """
        self.control_data_df = DataFrame(self.control_data)
        series_control_result_series = self.control_data_df["是否通过"]
        is_all_condition_pass_tuple = (True, "测试通过")
        for is_condition_value in series_control_result_series:
            if is_condition_value == 1:
                is_all_condition_pass_tuple = (False, "测试失败!!")
                break
        return is_all_condition_pass_tuple
