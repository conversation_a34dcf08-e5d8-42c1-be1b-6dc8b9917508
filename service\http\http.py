import requests

# 登录逻辑
session = requests.Session()

url = 'https://192.168.2.103/action/login'
payload = {
    'username': 'admin',
    'password': 'Admin_123',
    'language': 0
}

# 默认情况下，requests.post(data=...) 会把 payload 当作 application/x-www-form-urlencoded 发送
resp = session.post(url, data=payload, verify=False)
print(resp)
print(session.cookies.get_dict())
if resp.status_code == 200 and session.cookies.get_dict().get("-goahead-session-"):
    print("登录NPB服务器成功")
else:
    print("登录NPB服务器失败")

# 获取数据逻辑
url = 'https://192.168.2.103/action/web_getAllFlowInfos?'
# 注意：不是 dict，而是一个已经拼好的字符串
body = 'web_getAllFlowInfo=web_getAllFlowInfos'

headers = {
    'Content-Type': 'text/plain; charset=UTF-8',
    # 如果还需要其它头，比如 Authorization，也一并加进来
    # 'Authorization': 'Bearer …',
}

resp = session.post(url, data=body, headers=headers, verify=False)
print(resp)
if resp.status_code == 200:
    response_data_json = resp.json()
    if response_data_json:
        print("获取npb监测数据成功")
    else:
        print("获取npb数据为空")
else:
    print("发送获取npb数据请求失败")

# 发送和接受错误数清零逻辑
url = "https://192.168.2.103/action/web_clearFlow?"
headers = {
    'Content-Type': 'text/plain; charset=UTF-8',
    # 如果还需要其它头，比如 Authorization，也一并加进来
    # 'Authorization': 'Bearer …',
}
body = "flow_port=CE11"
resp = session.post(url, data=body, headers=headers, verify=False)
print(resp)
if resp.status_code == 200 and resp.text:
    print("错误数清零成功")
else:
    print("错误数清零失败")