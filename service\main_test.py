from __future__ import annotations
from datetime import datetime, timedelta
import os
from typing import Dict, List, TYPE_CHECKING
from time import sleep
from collections import defaultdict

import pandas as pd
from openpyxl import Workbook

from service.common.my_logger import logger
from front.core.signals import signals
from service.common.const import (
    BASE_OUTPUT_FILE_PATH,
    EXCEL_NAME_TIME_FORMAT,
    STANDARD_DATETIME_FORMAT,
    CONTROL_VALUE_LIST,
    NPB_DEFAULT_USERNAME,
    NPB_DEFAULT_PASSWORD,
    JUDGE_ALL_MODULE_STABLE_TIME_GAP,
    SLEEP_TIME_AFTER_ALL_MODULE_STABLE,
    OUTPUT_KEY_LIST,
    ERROR_KEY_LISY,
    RESULT_STR_MAPPER,
    OCT_DEFAULT_USERNAME,
    OCT_DEFAULT_PASSWROD,
)
from config import global_store
from service.common.ssh_device import SshDevice
from service.ssh.ssh import SshConnection
from service.common.excel import output_excel_sheet
from service.http.npb_http import NpbHttpClinet
from service.common.process import (
    judge_is_all_module_stable,
    get_oct_slot_to_send_error_and_receive_error_nums_mapper,
)

if TYPE_CHECKING:
    from service.common.mes_strategy import FeatureMesHanlder


def main_test(
    oct_ip: str,
    oct_ssh_port: int,
    config_list: list[dict],
    single_loop_time: int,
    test_start_time: datetime,
):
    # 创建文件输出目录
    if not os.path.exists(BASE_OUTPUT_FILE_PATH):
        os.makedirs(BASE_OUTPUT_FILE_PATH, exist_ok=True)
    # 先检测模块是否能连通
    logger.info(f"开始尝试连接模块,配置：{config_list}")
    try:
        ip = str(oct_ip)
        port = int(oct_ssh_port)
    except Exception as e:
        err_msg = f"测试终止,连接配置错误,port只能为数字"
        logger.error(err_msg)
        raise Exception(err_msg)
    logger.info(f"开始连接OCT设备,ip={ip},port={port}")
    global_store.set_context("test_running", True)
    # 发送创建表格行的命令，无论是否有模块都创建8行表格
    oct_slot_to_npb_ip_port_tuple_list_mapper = defaultdict(list)
    oct_slot_to_npb_ip_list_mapper = defaultdict(list)
    oct_slot_to_npb_port_list_mapper = defaultdict(list)
    npb_ip_set = set()
    oct_slot_set = set()
    for oct_npb_config_row in config_list:
        oct_slot = oct_npb_config_row["oct_slot"]
        npb_ip = oct_npb_config_row["npb_ip"]
        npb_data_port = oct_npb_config_row["npb_data_port"]
        oct_slot_set.add(oct_slot)
        if npb_ip not in oct_slot_to_npb_ip_list_mapper[oct_slot]:
            oct_slot_to_npb_ip_list_mapper[oct_slot].append(npb_ip)
        npb_ip_set.add(npb_ip)
        oct_slot_to_npb_port_list_mapper[oct_slot].append(npb_data_port)
        oct_slot_to_npb_ip_port_tuple_list_mapper[oct_slot].append(
            (npb_ip, npb_data_port)
        )
    # 根据OCT_NPB_MAPPER配置生成UI表格
    for oct_slot in sorted(oct_slot_set):
        row_dict = {
            "ip": oct_ip,
            "port": oct_ssh_port,
            "slot": oct_slot,
            "npb_ip": "-".join(oct_slot_to_npb_ip_list_mapper[oct_slot]),
            "npb_data_port": "-".join(oct_slot_to_npb_port_list_mapper[oct_slot]),
        }
        signals.table_create_single_row_signal.emit(row_dict)
    # 读取NPB数据
    npb_http_client_dict: dict[str, NpbHttpClinet] = {}
    for npb_ip in npb_ip_set:
        npb_http_client_dict.update({npb_ip: NpbHttpClinet(npb_ip)})
    npb_http_config: dict[str, dict[str, str]] = global_store.get_config(
        "npb_http_config"
    )
    # key:(npb_ip, npb_data_port),value:npb_data_doc
    for npb_ip, npb_http_client in npb_http_client_dict.items():
        username = npb_http_config.get(npb_ip, {}).get("username", NPB_DEFAULT_USERNAME)
        password = npb_http_config.get(npb_ip, {}).get("password", NPB_DEFAULT_PASSWORD)
        # 登录
        npb_http_client.login(username, password)
    # 获取npb数据从发送速率和接受速率判断每个配置的端口是否稳定,不稳定就循环读取一直等到稳定
    is_module_all_stable_flag = False
    judge_is_module_all_stable_start_time = datetime.now()
    while not is_module_all_stable_flag:
        # todo 如果判断是否稳定持续时间大于5分钟则弹窗报错,告知未稳定的模块和端口,测试结束
        (
            is_module_all_stable_flag,
            unstable_module_info_list,
            oct_slot_to_send_error_nums_mapper,
            oct_slot_to_receive_error_nums_mapper,
        ) = judge_is_all_module_stable(
            oct_ip,
            npb_http_client_dict,
            oct_slot_set,
            oct_slot_to_npb_ip_port_tuple_list_mapper,
        )
        # 更新UI界面上的发送错误数和接受错误数
        for oct_slot in oct_slot_set:
            npb_send_errors_tuple_list = oct_slot_to_send_error_nums_mapper[oct_slot]
            npb_send_errors_str_list = [
                f"{npb_send_errors_tuple[0]}={npb_send_errors_tuple[1]}"
                for npb_send_errors_tuple in npb_send_errors_tuple_list
            ]
            npb_receive_errors_tuple_list = oct_slot_to_receive_error_nums_mapper[
                oct_slot
            ]
            npb_receive_errors_str_list = [
                f"{npb_receive_errors_tuple[0]}={npb_receive_errors_tuple[1]}"
                for npb_receive_errors_tuple in npb_receive_errors_tuple_list
            ]
            update_dict = {
                "npb_send_errors": ",".join(npb_send_errors_str_list),
                "npb_receive_errors": ",".join(npb_receive_errors_str_list),
            }
            signals.table_update_single_row_signal.emit((oct_slot,), update_dict)
        # 每两次判断是否全部稳定之间间隔10秒
        sleep(JUDGE_ALL_MODULE_STABLE_TIME_GAP)
        judge_is_module_all_stable_end_time = datetime.now()
        if (
            judge_is_module_all_stable_end_time - judge_is_module_all_stable_start_time
            > timedelta(seconds=300)
        ):
            unstable_module_info_str = ""
            for unstable_module_info in unstable_module_info_list:
                unstable_module_info_str_single = f'oct_ip:{unstable_module_info["oct_ip"]},\
oct_slot:{unstable_module_info["oct_slot"]},npb_ip:{unstable_module_info["npb_ip"]},\
npb_data_port:{unstable_module_info["npb_data_port"]},发送速率:{unstable_module_info["发送速率"]},\
接收速率:{unstable_module_info["接收速率"]}'
                unstable_module_info_str = unstable_module_info_str + unstable_module_info_str_single
            raise TimeoutError(f"判断模块是否稳定超过5分钟仍有模块不稳定,模块信息:{unstable_module_info_str}")
    logger.info(
        f"模块已全部稳定,即将进入清零操作前{JUDGE_ALL_MODULE_STABLE_TIME_GAP}秒的等待时间"
    )
    logger.info(f"开始清零前的等待,等待时间:{SLEEP_TIME_AFTER_ALL_MODULE_STABLE}秒")
    sleep(SLEEP_TIME_AFTER_ALL_MODULE_STABLE)
    logger.info("结束清零前的等待")
    for oct_slot in oct_slot_set:
        npb_ip_port_tuple_list = oct_slot_to_npb_ip_port_tuple_list_mapper[oct_slot]
        for npb_ip, npb_data_port in npb_ip_port_tuple_list:
            npb_http_client = npb_http_client_dict[npb_ip]
            npb_http_client.clear_error_nums(npb_data_port)
    logger.info("所有清零全部完成")
    # 开始读取OCT信息
    logger.info("开始读取OCT数据")
    oct_slot_list = sorted(oct_slot_set)
    oct_ssh_config: dict[str, dict[str, str]] = global_store.get_config(
        "oct_ssh_config"
    )
    oct_user_name = oct_ssh_config.get(ip, {}).get("usernmae", OCT_DEFAULT_USERNAME)
    oct_password = oct_ssh_config.get(ip, {}).get("password", OCT_DEFAULT_PASSWROD)
    ssh_device = SshDevice(
        SshConnection(ip, port, oct_user_name, oct_password), oct_slot_list
    )
    ssh_device.init_device_coon()
    ssh_device.check_module_exists()
    result_dict: dict = ssh_device.read_online_display_cfp2_dco_info()
    slot_sn_tuple_list = []
    for module_index, result_str in result_dict.items():
        sn = ssh_device.get_sn_by_result_str(result_str)
        slot_sn_tuple_list.append((module_index, sn))
        signals.table_update_single_row_signal.emit((module_index,), {"sn": sn})
    # todo 先查询一次用来查询MES相关信息,在MES系统里面开始工序,生成结果文件
    mes_feature_handler: FeatureMesHanlder = global_store.get_context(
        "mes_feature_handler"
    )
    mes_feature_handler.before_main_test(slot_sn_tuple_list)
    test_start_time_str = test_start_time.strftime(EXCEL_NAME_TIME_FORMAT)
    result_file_path_dict = {}
    result_list_dict: Dict[int, List] = {}
    error_list_dict: Dict[int, List] = {}
    for module_index, result_str in result_dict.items():
        vendor_sn = ssh_device.get_sn_by_result_str(result_str)
        result_file_name = f"{test_start_time_str}__{vendor_sn}.xlsx"
        result_file_path = f"{BASE_OUTPUT_FILE_PATH}/{result_file_name}"
        if not os.path.exists(result_file_path):
            wb = Workbook()
            # 默认表格Sheet名称修改为data
            default_ws = wb.active
            default_ws.title = "data"
            wb.save(result_file_path)
            result_file_path_dict.update({module_index: result_file_path})
            result_list_dict.update({module_index: []})
            error_list_dict.update({module_index: []})
    # 在test_running变为False之前持续读取设备数据并更新excel表格和UI表格,中途就不合格的UI背景变红
    slot_to_is_all_time_pass_dict = {oct_slot: True for oct_slot in oct_slot_list}
    while True:
        loop_start_time = datetime.now()
        loop_start_time_str = loop_start_time.strftime(STANDARD_DATETIME_FORMAT)
        # 获取发送错误数和接受错误数信息
        oct_slot_to_send_error_nums_mapper, oct_slot_to_receive_error_nums_mapper = (
            get_oct_slot_to_send_error_and_receive_error_nums_mapper(
                npb_http_client_dict,
                oct_slot_set,
                oct_slot_to_npb_ip_port_tuple_list_mapper,
            )
        )
        result_dict: dict = ssh_device.read_online_display_cfp2_dco_info()
        # 下面for循环里面的module_index即是while循环外面的oct_slot
        for module_index, result_str in result_dict.items():
            single_module_result_dict = ssh_device.trans_result_str_to_key_value_dict(
                result_str
            )
            single_module_result_dict.update(
                {
                    "datetime": loop_start_time_str,
                    "Network FEC Uncorr Blk Cnt": int(
                        single_module_result_dict["Network FEC Uncorr Blk Cnt"], 16
                    ),
                }
            )
            is_this_time_pass = True
            value_trans_mapper = {"int": int, "float": float}
            for control_dict in CONTROL_VALUE_LIST:
                control_key = control_dict["name"]
                value_type = control_dict["value_type"]
                control_value = value_trans_mapper[value_type](
                    single_module_result_dict[control_key]
                )
                min_value = control_dict["min_value"]
                max_value = control_dict["max_value"]
                if control_value < min_value or control_value > max_value:
                    is_this_time_pass = False
                    # 以数据流的形式记录没过卡控的信息
                    error_info_row = {
                        "datetime": loop_start_time_str,
                        "name": control_key,
                        "value": control_value,
                        "min_value": min_value,
                        "max_value": max_value,
                        "is_pass": False,
                    }
                    error_list_dict[module_index].append(error_info_row)
            # 检查发送错误数和接受错误数是否大于零
            send_error_nums_tuple_list = oct_slot_to_send_error_nums_mapper[
                module_index
            ]
            receive_error_nums_tuple_list = oct_slot_to_receive_error_nums_mapper[
                module_index
            ]
            send_error_nums_str_list = []
            receive_error_nums_str_list = []
            for (
                npb_ip,
                npb_data_port,
                send_error_nums,
            ) in send_error_nums_tuple_list:
                if send_error_nums > 0:
                    is_this_time_pass = False
                    error_info_row = {
                        "datetime": loop_start_time_str,
                        "name": f"send_error_nums {npb_ip} {npb_data_port}",
                        "value": send_error_nums,
                        "min_value": 0,
                        "max_value": 0,
                        "is_pass": False,
                    }
                    error_list_dict[module_index].append(error_info_row)
                send_error_nums_str_list.append(f"{npb_data_port}={send_error_nums}")
            for (
                npb_ip,
                npb_data_port,
                receive_error_nums,
            ) in receive_error_nums_tuple_list:
                if receive_error_nums > 0:
                    is_this_time_pass = False
                    error_info_row = {
                        "datetime": loop_start_time_str,
                        "name": f"receive_error_nums {npb_ip} {npb_data_port}",
                        "value": receive_error_nums,
                        "min_value": 0,
                        "max_value": 0,
                        "is_pass": False,
                    }
                    error_list_dict[module_index].append(error_info_row)
                receive_error_nums_str_list.append(
                    f"{npb_data_port}={receive_error_nums}"
                )
            send_error_nums_str = ",".join(send_error_nums_str_list)
            receive_error_nums_str = ",".join(receive_error_nums_str_list)
            single_module_result_dict.update(
                {
                    "is_this_time_pass": is_this_time_pass,
                    "npb_send_errors": send_error_nums_str,
                    "npb_receive_errors": receive_error_nums_str,
                }
            )
            if is_this_time_pass is False:
                slot_to_is_all_time_pass_dict[module_index] = False
                signals.table_change_row_color_signal.emit((module_index,))
            result_list_dict[module_index].append(single_module_result_dict)
            # 更新UI界面表格
            signals.table_update_single_row_signal.emit(
                (module_index,), single_module_result_dict
            )
            # 输出excel文件
            output_excel_sheet(
                pd.DataFrame(result_list_dict[module_index]),
                pd.DataFrame(error_list_dict[module_index]),
                OUTPUT_KEY_LIST,
                ERROR_KEY_LISY,
                result_file_path_dict[module_index],
                "data",
                "error",
                RESULT_STR_MAPPER[slot_to_is_all_time_pass_dict[module_index]],
                slot_to_is_all_time_pass_dict[module_index]
            )
        loop_end_time = datetime.now()
        loop_delta_time = loop_end_time - loop_start_time
        single_loop_time_delta = timedelta(seconds=single_loop_time)
        sleep_time_delta = single_loop_time_delta - loop_delta_time
        if global_store.get_context("test_running") is False:
            break
        if sleep_time_delta.total_seconds() > 0:
            sleep(sleep_time_delta.total_seconds())
    # todo 判定是否合格结果，数据和结果上传MES
    mes_feature_handler.after_main_test(
        slot_sn_tuple_list, slot_to_is_all_time_pass_dict, result_file_path_dict
    )
