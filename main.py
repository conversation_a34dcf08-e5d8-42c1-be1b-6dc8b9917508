import sys
from multiprocessing import Process, freeze_support

from PySide6.QtWidgets import QApplication
from oct_npm_mapper import OctNpbMapper


def single_process(oct_ip: str, oct_ssh_port: int, oct_npb_config_list: list[dict]):
    from config import global_store
    global_store.set_context("oct_ip", oct_ip)
    global_store.set_context("oct_ssh_port", oct_ssh_port)
    global_store.set_context("oct_npb_config_list", oct_npb_config_list)
    from front.main_window import MainWindow
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())


if __name__ == "__main__":
    """ single_process(
        "***************",
        22,
        [
            {"oct_slot": 1, "npb_ip": "*************", "npb_data_port": "CE2"},
            {"oct_slot": 1, "npb_ip": "*************", "npb_data_port": "CE3"},
            {"oct_slot": 1, "npb_ip": "*************", "npb_data_port": "CE4"},
            {"oct_slot": 1, "npb_ip": "*************", "npb_data_port": "CE5"},
            {"oct_slot": 2, "npb_ip": "*************", "npb_data_port": "CE6"},
            {"oct_slot": 2, "npb_ip": "*************", "npb_data_port": "CE7"},
            {"oct_slot": 2, "npb_ip": "*************", "npb_data_port": "CE8"},
            {"oct_slot": 2, "npb_ip": "*************", "npb_data_port": "CE9"},
            {"oct_slot": 3, "npb_ip": "*************", "npb_data_port": "CE10"},
            {"oct_slot": 3, "npb_ip": "*************", "npb_data_port": "CE11"},
            {"oct_slot": 3, "npb_ip": "*************", "npb_data_port": "CE2"},
            {"oct_slot": 3, "npb_ip": "*************", "npb_data_port": "CE3"},
        ],
    ) """
    freeze_support()
    oct_npb_mapper = OctNpbMapper()
    oct_npb_mapper.check_config_and_generate_config()
    process_list: list[Process] = []
    for (
        oct_ip,
        oct_ssh_port,
    ), oct_npb_config_list in oct_npb_mapper.oct_ip_port_to_config_list_mapper.items():
        p = Process(
            target=single_process, args=(oct_ip, oct_ssh_port, oct_npb_config_list)
        )
        process_list.append(p)
    for p in process_list:
        p.start()
    for p in process_list:
        p.join()
