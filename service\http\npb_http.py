import requests
from service.common.my_logger import logger


class NpbHttpClinet:
    def __init__(self, ip: str):
        self.ip = ip
        self.login_flag = False
        self.login_url = rf"https://{ip}/action/login"
        self.npb_data_query_url = rf"https://{ip}/action/web_getAllFlowInfos?"
        self.clear_error_nums_url = rf"https://{ip}/action/web_clearFlow?"
        self.session = requests.Session()

    def login(self, user_name: str, password: str, language: int = 0):
        payload = {"username": user_name, "password": password, "language": language}
        resp = self.session.post(self.login_url, data=payload, verify=False)
        if resp.status_code == 200 and self.session.cookies.get_dict().get(
            "-goahead-session-"
        ):
            logger.info(f"ip:{self.ip}登录NPB服务器成功")
            self.login_flag = True
        else:
            self.login_flag = False
            error_msg = f"ip:{self.ip},登录NPB服务器失败,status_code:{resp.status_code},resp:{resp}"
            raise Exception(error_msg)

    def query_npb_data(self) -> list[dict]:
        if not self.login_flag:
            self.login()
        body = "web_getAllFlowInfo=web_getAllFlowInfos"
        headers = {
            "Content-Type": "text/plain; charset=UTF-8",
        }
        resp = self.session.post(
            self.npb_data_query_url, data=body, headers=headers, verify=False
        )
        if resp.status_code == 200:
            response_data_json = resp.json()
            if response_data_json:
                logger.info(
                    f"ip:{self.ip},获取NPB监测数据成功,data:{response_data_json}"
                )
                return response_data_json
            else:
                error_msg = f"ip:{self.ip},获取NPB数据为空"
                raise Exception(error_msg)
        else:
            error_msg = f"ip:{self.ip},发送获取npb数据请求失败,status_code:{resp.status_code},resp:{resp}"
            raise Exception(error_msg)

    def clear_error_nums(self, npb_port: str):
        headers = {"Content-Type": "text/plain; charset=UTF-8"}
        body = f"flow_port={npb_port}"
        resp = self.session.post(self.clear_error_nums_url, data=body, headers=headers, verify=False)
        if resp.status_code == 200 and resp.text:
            logger.info(f"ip:{self.ip},npb_port:{npb_port},错误数清零成功")
        else:
            error_msg = f"ip:{self.ip},npb_port:{npb_port},错误数清零失败,status_code:{resp.status_code},resp:{resp}"
            raise Exception(error_msg)
