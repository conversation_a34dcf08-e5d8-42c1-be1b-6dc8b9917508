result_1_8 = """display cfp2-dco 

   CFP2_DCO Module No.1 information:

    Gene Info: 
      Tx Laser Mode                 :	ON
      Power Mode                    :	Normal Power
      Key Status                    :	Normal
      MdDev Mode Value              :	0x00
      MdDev KeyStatus Value         :	0x00
      MdDev Voltage Value           :	3372 mV
      MdDev Current Value           :	4789 mA
      MdDev Power Value             :	16148 mW

    Manu Info: 
      Vendor Name                   :	Accelink
      Vendor PN                     :	OCT-02C2-A3CN8  
      Vendor SN                     :	OCT2N825030165  
      Vendor DateCode               :	2025-03-11

    Basic Info: 
      Connector Type                :	oc-opt-types:LC_CONNECTOR
      Ethernet App Code             :	oc-opt-types:ETH_UNDEFINED
      Host Lane Number              :	8
      Network Lane Number           :	1
      Device Technology             :	DBR,N/A
      Transceiver Temperature Th.   :	0.00 C, 70.00 C
      VCC Alarm Th.                 :	3.22 V, 3.46 V
      Tx Power Alarm Th.            :	0.00 dBm, 0.00 dBm
      Rx Power Alarm Th.            :	-21.00 dBm, 13.00 dBm
      Rx High Alarm Min Max Th      :	0.00 dBm, 15.00 dBm
      Rx Low Alarm Min Max Th       :	-40.00 dBm, -15.00 dBm
      Rx High SetValue              :	8.00 dBm
      Rx Low SetValue               :	-40.00 dBm
      Rx SetValue Status            :	INACTIVE
      Tx Min Laser Freq             :	191150.00 GHz
      Rx Min Laser Freq             :	191150.00 GHz
      Tx Rx Max Laser Freq          :	196100.00 GHz
      Laser Tuning Capability       :	6.25 GHz 12.5 GHz 25 GHz 50 GHz 100 GHz 

    Real Info: 
      Module State                  :	0x0020
      General Status Latch          :	0x0006
      Alarm and Warning Latch       :	0x0000
      Fast Switching Mode           :	0
      Module Temperature            :	55.00 C
      Module Power Supply           :	3.37 V
      SOA Bias                      :	0.00 mA
      Tx Laser Bias                 :	184.20 mA
      Network Alm Warning 1 Latch   :	0x0000
      Network Alm Warning 2 Latch   :	0x0000
      TX Channel Ctrl               :	6.25 GHz grid spacing, Channel number:25
      Network TX Laser Output       :	15.50 dBm
      Network TX Laser Temp         :	55.74 C
      Desired TX Output             :	0.00 dBm
      TX Current Freq               :	191300.00 GHz
      RX Current Freq               :	191300.00 GHz
      Current Output Power          :	0.00 dBm
      Current Input Power           :	-0.40 dBm
      Network Rx Outage Duration    :	809 ms
      General Mode Control MSB1     :	0x004d
      General Mode Control MSB2     :	0x0503
      Network Current BER           :	3.650005e-05
      Network Current BER[max]      :	4.285703e-05
      Network FEC Uncorr Blk Cnt    :	0x00000000
      Network FEC Uncorr Blk Reset  :	204108
      Network Post BER              :	0.000000e+00
      Network Loopback Control      :	0x0000
      Client Tx FEC Uncorr Blk SMR  :	0 0 0 0
      Client Tx FEC Corr Bit SMR    :	0.000000e+00 0.000000e+00 0.000000e+00 0.000000e+00
      Client Tx Post FEC Ber PM-Int :	0.000000e+00 0.000000e+00 0.000000e+00 0.000000e+00
      Client Tx FEC Ber over PM-Int :	0.000000e+00 0.000000e+00 0.000000e+00 0.000000e+00
      
      Type       CD(ps/nm)  DGD(ps)    SOPMD(ps^2)     SOP(rad/s) PDL(dB)    Q(dB)      CFO(MHz)   SNR(dB)    OSNR(dB)   
      Current    2          0          163             0          1.2        11.9       1          20.8       32.3       
      Avg        2          0          163             0          1.2        11.9       0          20.8       32.3       
      Max        2          0          163             0          1.2        11.9       1          20.8       32.3       
      Min        2          0          163             0          1.2        11.9       -1         20.8       32.3       

[ACCELINK-LPU2_2-1/8]"""

if __name__ == "__main__":
    import re

    key_list = [
        "Vendor SN",
        "Module Temperature",
        "Module Power Supply",
        "SOA Bias",
        "Tx Laser Bias",
        "Network TX Laser Temp",
        "RX Current Freq",
        "Current Output Power",
        "Current Input Power",
        "Network Current BER",
        "Network FEC Uncorr Blk Cnt",
        "Network FEC Uncorr Blk Reset",
        "Network FEC Uncorr Blk Rate",
    ]
    key_str = "|".join(key_list)
    pattern = re.compile(rf"({key_str})\s*:\s*(\S+)")
    matches = pattern.findall(result_1_8)

    # 将结果转换为字典
    result = {key: value for key, value in matches}

    # 输出结果
    print(result)

    key_list_multi = [
        "Client Tx FEC Uncorr Blk SMR",
        "Client Tx FEC Corr Bit SMR",
        "Client Tx Post FEC Ber PM-Int",
        "Client Tx Post FEC Ber PM-Int",
    ]
    key_str_multi = "|".join(key_list_multi)
    pattern_multi = re.compile(rf"({key_str_multi})\s*:\s*(.*?)\s*(?=\n|$)")
    matches_multi = pattern_multi.findall(result_1_8)
    result_multi = {key: value for key, value in matches_multi}
    print(result_multi)

    test_value = result_multi["Client Tx FEC Corr Bit SMR"].split(" ")[0]
    print(float(test_value) == 0)
    for k, v in result_multi.items():
        values = v.split(" ")
        for value_index, value in enumerate(values):
            result.update({f"{k}[{value_index + 1}]": value})
    print(result)

    pattern = re.compile(r"Vendor SN\s*:\s*(\S+)")
    match = pattern.search(result_1_8)
    if match:
        result = match.group(1)
        print(result)