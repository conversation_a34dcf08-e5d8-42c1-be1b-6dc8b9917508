def format_timedelta(td):
    # 获取总秒数，并转换为整数
    total_seconds = int(td.total_seconds())
    
    # 如果是负数，则记录符号，然后取绝对值处理
    sign = '-' if total_seconds < 0 else ''
    total_seconds = abs(total_seconds)
    
    days = total_seconds // 86400  # 86400秒 = 1天
    hours = (total_seconds % 86400) // 3600
    minutes = (total_seconds % 3600) // 60
    seconds = total_seconds % 60
    
    return f"{sign}{days}天{hours:02d}小时{minutes:02d}分{seconds:02d}秒"