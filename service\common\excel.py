import unicodedata

from openpyxl.worksheet.worksheet import Worksheet
from openpyxl.workbook.workbook import Workbook
from openpyxl.styles import Font, Alignment, PatternFill
from pandas import DataFrame, ExcelWriter


def calculate_width(text: str) -> int:
    """计算文本的显示宽度。

    根据 Unicode 的 East Asian Width 属性计算文本宽度，宽字符（'F' 或 'W'）计 2 个单位，
    其他字符计 1 个单位，返回文本的总宽度。

    Args:
        text (str): 要计算宽度的文本字符串。

    Returns:
        int: 文本的总宽度。
    """
    width = 0
    for char in text:
        # 判断是否是宽字符（如汉字）
        if unicodedata.east_asian_width(char) in ["F", "W"]:  # 'F' 和 'W' 表示宽字符
            width += 2  # 宽字符占2个单位宽度
        else:
            width += 1  # 普通字符占1个单位宽度
    return width


def auto_adjust_column_width(sheet: Worksheet):
    """自动调整 Excel 工作表中各列的宽度。

    遍历工作表中所有列，计算每一列所有单元格文本的最大宽度，
    并将该列宽度设置为最大宽度加上一定的缓冲空间，以改善 Excel 文件的显示效果。

    Args:
        sheet (Worksheet): 要调整列宽的 Excel 工作表。
    """
    for col in sheet.columns:
        max_length = 0
        column = col[0].column_letter  # 获取列的字母标识
        for cell in col:
            try:
                if isinstance(cell.value, (int, float)):
                    text_length = calculate_width(
                        f"{cell.value:.15g}"
                    )  # 使用字符串形式，避免浮点数显示问题
                else:
                    text_length = calculate_width(str(cell.value))

                if text_length > max_length:
                    max_length = text_length
            except:
                pass
        adjusted_width = max_length + 5  # 加一点额外的缓冲空间
        sheet.column_dimensions[column].width = adjusted_width


def output_excel_sheet(
    df_data: DataFrame,
    df_error: DataFrame,
    columns_data: list[str],
    columns_error: list[str],
    excel_file_path: str,
    sheet_name_data: str,
    sheet_name_error: str,
    result_str: str,
    is_all_time_pass: bool,
):
    with ExcelWriter(
        excel_file_path, engine="openpyxl", mode="a", if_sheet_exists="replace"
    ) as writer:
        # 将 DataFrame 写入 Excel 的指定 Sheet
        df_data.to_excel(
            writer,
            sheet_name=sheet_name_data,
            startrow=1,
            index=False,
            columns=columns_data,
        )
        # 获取 workbook 和 worksheet 对象
        workbook: Workbook = writer.book
        worksheet_1: Worksheet = writer.sheets[sheet_name_data]
        auto_adjust_column_width(worksheet_1)
        if df_error.size:
            df_error.to_excel(
                writer,
                sheet_name=sheet_name_error,
                index=False,
                columns=columns_error,
            )
            worksheet_2: Worksheet = writer.sheets[sheet_name_error]
            auto_adjust_column_width(worksheet_2)
        # 添加合并行
        merge_text = result_str
        num_columns = 2  # 数据的列数
        font_color_mapper = {True: "00FF00", False: "FF0000"}
        font_color = font_color_mapper.get(is_all_time_pass, "000000")
        bg_color_mapper = {True: "DFFFD6", False: "FFD6D6"}
        bg_color = bg_color_mapper.get(is_all_time_pass, "FFFFFF")

        worksheet_1.merge_cells(
            start_row=1, start_column=1, end_row=1, end_column=num_columns
        )
        merged_cell = worksheet_1.cell(row=1, column=1)
        merged_cell.value = merge_text
        merged_cell.font = Font(bold=True, color=font_color, size=14)
        merged_cell.alignment = Alignment(horizontal="center", vertical="center")
        merged_cell.fill = PatternFill(
            start_color=bg_color, end_color=bg_color, fill_type="solid"
        )
        # 保存文件
        workbook.save(excel_file_path)
