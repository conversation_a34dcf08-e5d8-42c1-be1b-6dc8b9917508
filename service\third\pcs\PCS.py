import hashlib
import json
import os
import time
import zipfile

import requests
from front.core.signals import signals

from config import global_store
from service.common.my_logger import logger

"""
MES客户端接口的Python实现
"""
'''
单独运行这个文件时，需要把config.py的 configPath 改成绝对路径（就是解注释两行代码，但是打包的时候记得注释回去，否则exe会运行报错）
'''

config = global_store.get_whole_config()

class PCS:
    def __init__(self):
        self.url_base = config['pcs_server']['url_server']
        self.headers = {"content-type": "application/json"}
        self.user_info = {}
        self.token = ''

        self.inti_product_related_params()

    def inti_product_related_params(self):
        self.process_info = {}
        self.process_id = {}
        self.material_data = {}
        self.device_data = {}
        self.test_data = {}
        self.product_info = {}
        self.firmware_file_path = ''

    def check_login(func):
        def wrapper(self, *args, **kwargs):
            if not self.token:
                signals.errorSingal.emit(-1, "请先登录！")
                return -1, "请先登录！"
            return func(self, *args, **kwargs)
        return wrapper

    def login(self, employee_no, raw_password, work_site):
        """
        登录，登录信息从ini文件中获取
        需要获取其返回的token
        """
        try:
            step_name = 'login'
            self.user_info['employee_no'] = employee_no
            self.user_info['raw_password'] = raw_password
            self.user_info['work_site'] = work_site

            data = config['pcs_version'] | self.user_info
            # 密码md5加密
            data['password'] = hashlib.md5(self.user_info['raw_password'].encode()).hexdigest()
            response = requests.post(url=self.url_base + step_name, headers=self.headers, data=json.dumps(data))

            if response.status_code == 200:
                resp = response.content.decode('utf-8')
                if 'error' not in resp:
                    self.token = response.json()['token']
                    logger.info(f'{employee_no} 登录成功!')
                    return 0, f"登录成功！"
                else:
                    logger.warning(f'{employee_no} 登录失败: {resp}')
                    error = response.json()['error']
                    return -1, f"登录失败！ {error}"
            else:
                logger.error(f'{employee_no} 登录 请求失败！code: {response.status_code}')
                return -2, f"登录 请求失败！code: {response.status_code}"
        except Exception as err:
            logger.error(f'{employee_no} 登录失败: {err}')
            return -3, f"登录失败！"

    def logout(self):
        self.token = ''


    @check_login
    def process_test_data(self, mn, process):
        """
        查询测试数据
        """
        try:
            step_name = 'process_test_data'
            process_info = {
                'mn': mn,
                'process': process,
                'token': self.token
            }
            response = requests.get(url=self.url_base + step_name, params=process_info)
            if response.status_code == 200:
                resp = response.content.decode('utf-8')
                if 'error' not in resp:
                    logger.info(f'{mn}: 查询测试数据 {process}  成功!')
                    # 取出测试数据相关的键值对
                    raw = json.loads(resp)
                    test_data = []
                    for item in raw:
                        test_data.append({key: value for key, value in item.items() if key in ['is_pass', 'item_grade', 'item_name', 'item_value', 'time']})
                    return 0, test_data
                else:
                    logger.warning(f'{mn}: 查询测试数据 {process} 失败!: {resp}')
                    return -1, resp
            else:
                logger.error(f'{mn}: 查询测试数据 {process} 请求失败！code: {response.status_code}')
                return -2, response.status_code
        except Exception as err:
            logger.error(f'{mn}: 查询测试数据 {process}  失败: {err}!')
            return -3, err

    @check_login
    def process_material_data(self, mn, process):
        """
        查询测试数据
        """
        try:
            step_name = 'process_material_data'
            process_info = {
                'mn': mn,
                'process': process,
                'token': self.token
            }
            response = requests.get(url=self.url_base + step_name, params=process_info)
            if response.status_code == 200:
                resp = response.content.decode('utf-8')
                if 'error' not in resp:
                    logger.info(f'{mn}: 查询物料数据 {process}  成功!')
                    # 取出物料数据相关的键值对
                    raw = json.loads(resp)
                    material_data = []
                    for item in raw:
                        material_data.append({key: value for key, value in item.items() if key in ['lot_no', 'material_pn', 'material_sn', 'quantity', 'spec', 'time']})
                    return 0, material_data
                else:
                    logger.warning(f'{mn}: 查询物料数据 {process} 失败!: {resp}')
                    return -1, resp
            else:
                logger.error(f'{mn}: 查询物料数据 {process} 请求失败！code: {response.status_code}')
                return -2, response.status_code
        except Exception as err:
            logger.error(f'{mn}: 查询物料数据 {process}  失败: {err}!')
            return -3, err

    @check_login
    def start_process(self, mn, process, clear_data=False, continue_process=False):
        """
        开始工序，先开始工序才能上传工序数据
        需要获取其返回的process_id
        """
        if not mn or mn == '':
            return -1, f'mn 不能为空！'
        if not process or process == '':
            return -1, f'process 不能为空！'
        try:
            step_name = 'start_process'
            self.process_info[mn] = {
                'mn': mn,
                'process': process
            }
            data = self.process_info[mn]
            data['clear_data'] = clear_data
            data['continue_process'] = continue_process
            response = requests.post(url=self.url_base + step_name, params={'token': self.token}, headers=self.headers, data=json.dumps(data))

            if response.status_code == 200:
                self.process_id[mn] = response.json()  # 不同mn对应不同process_id
                self.material_data[mn] = []
                self.device_data[mn] = []
                self.test_data[mn] = []
                resp = response.content.decode('utf-8')
                if 'error' not in resp:
                    logger.info(f'{mn}: 开始工序 {process}  成功!')
                    return 0, f"开始工序 请求成功！ {resp}"
                else:
                    logger.warning(f'{mn}: 开始工序 {process} 失败!: {resp}')
                    return -1, resp
            else:
                logger.error(f'{mn}: 开始工序 {process} 请求失败！code: {response.status_code}')
                return -2, response.status_code
        except Exception as err:
            logger.error(f'{mn}: 开始工序 {process}  失败: {err}!')
            return -3, err


    @check_login
    def posit_process(self, mn, process):
        """
        转到工序
        """
        try:
            step_name = 'posit_process'
            self.process_info[mn] = {
                'mn': mn,
                'process': process
            }
            data = self.process_info[mn]
            response = requests.post(url=self.url_base + step_name, params={'token': self.token}, headers=self.headers, data=json.dumps(data))

            if response.status_code == 200:
                resp = response.content.decode('utf-8')
                if 'error' not in resp:
                    self.process_id[mn] = response.json()  # 不同mn对应不同process_id
                    self.material_data[mn] = []
                    self.device_data[mn] = []
                    self.test_data[mn] = []
                    logger.info(f'{mn}: 转到工序 {process}  成功!')
                    return 0, f"转到工序 请求成功！ {resp}"
                else:
                    logger.warning(f'{mn}: 转到工序 {process} 失败!: {resp}')
                    return -1, resp
            else:
                logger.error(f'{mn}: 转到工序 {process} 请求失败！code: {response.status_code}')
                return -2, response.status_code
        except Exception as err:
            logger.error(f'{mn}: 转到工序 {process}  失败: {err}!')
            return -3, err


    @check_login
    def cache_material_data(self, mn, lot_no, material_pn, quantity: float):
        dct = dict()
        dct['lot_no'] = lot_no
        dct['material_pn'] = material_pn
        dct['quantity'] = quantity
        self.material_data[mn].append(dct)


    @check_login
    def cache_device_data(self, mn, device_name, item_name, item_value):
        dct = dict()
        dct['device_name'] = device_name
        dct['item_name'] = item_name
        dct['item_value'] = item_value
        self.device_data[mn].append(dct)


    @check_login
    def cache_test_data(self, mn, item_name, item_value):
        dct = dict()
        dct['item_name'] = item_name
        dct['item_value'] = item_value
        self.test_data[mn].append(dct)


    @check_login
    def submit_process_data(self, mn):
        """
        上传数据
        需要token、 process_id
        """
        mn = str(mn)
        try:
            step_name = 'submit_process_data'
            process_data = dict()
            process_data['material_data'] = self.material_data[mn]
            process_data['device_data'] = self.device_data[mn]
            process_data['test_data'] = self.test_data[mn]
            process_data.update(self.process_id[mn])
            process_data.update(self.process_info[mn])
            response = requests.post(url=self.url_base + step_name, params={'token': self.token}, headers=self.headers, data=json.dumps([process_data]))

            if response.status_code == 200:
                resp = response.content.decode('utf-8')
                if 'error' not in resp:
                    logger.info(f'{mn}: 上传工序数据 成功!')
                    return 0, f"上传工序数据 成功！ {resp}"
                else:
                    logger.warning(f'{mn}: 上传工序数据 失败： {resp}!')
                    return -1, resp
            else:
                logger.error(f'{mn}: 上传工序数据 请求失败！code: {response.status_code}')
                return -2, response.status_code
        except Exception as err:
            logger.error(f'{mn}: 上传工序数据 失败: {err}!')
            return -3, err


    @check_login
    def finish_process(self, mn, is_pass, defect_category=None, defect_code=None):
        try:
            if type(is_pass) == str:
                if is_pass.lower() == 'true':
                    is_pass = True
                elif is_pass.lower() == 'false':
                    is_pass = False
                else:
                    return -3, "is_pass 类型错误！"
            step_name = 'finish_process'
            data = {'is_pass': is_pass} | self.process_info[mn] | self.process_id[mn]
            if not is_pass:
                data.update({"defect_category": str(defect_category), 'defect_code': str(defect_code)})
            response = requests.post(url=self.url_base + step_name, params={'token': self.token}, headers=self.headers, data=json.dumps(data))
            if response.status_code == 200:
                resp = response.content.decode('utf-8')
                if 'error' not in resp:
                    logger.info(f'{mn}: 结束工序 成功!')
                    return 0, f"结束工序 成功！"
                else:
                    logger.warning(f'{mn}: 结束工序 失败: {resp}!')
                    return -1, resp
            else:
                logger.error(f'{mn}: 结束工序 请求失败！code: {response.status_code}')
                return -2, response.status_code
        except Exception as err:
            logger.error(f'{mn}: 结束工序失败: {err}!')
            return -3, err


    @check_login
    def upload_test_data(self, mn, file_path):
        """
        上传文件
        :param mn:
        :param file_path:
        :return: int，str
        """
        try:
            step_name = 'upload_test_data'
            file = {'test_file': open(file_path, 'rb')}
            params = self.process_info[mn] | self.process_id[mn] | {'token': self.token}
            response = requests.post(self.url_base + step_name, params=params, files=file)
            if response.status_code == 200:
                resp = response.content.decode('utf-8')
                if 'error' not in resp:
                    logger.info(f'{mn}: 上传测试数据文件 {file_path} 成功!')
                    return 0, f"上传文件 请求成功！ {resp}"
                else:
                    logger.warning(f'{mn}: 上传测试数据文件 {file_path} 失败:{resp}!')
                    return -1, resp
            else:
                logger.error(f'{mn}: 上传测试数据文件 {file_path} 请求失败！code: {response.status_code}')
                return -2, response.status_code
        except Exception as err:
            logger.warning(f'{mn}: 上传测试数据文件 {file_path} 失败:{err}!')
            return -3, err

    @check_login
    def get_product_info(self, sn):
        """
        查询产品信息
        :param sn:
        :return str:
        """
        try:
            step_name = 'product_info'
            params = {'token': self.token} | {"sn": sn}
            response = requests.get(self.url_base + step_name, params=params)
            if response.status_code == 200:
                resp = response.content.decode('utf-8')
                if 'error' not in resp:
                    logger.info(f'{sn}: 查询产品信息 成功!')
                    resp = json.loads(resp)
                    self.product_info[sn] = resp  #产品信息
                    return 0, resp
                else:
                    logger.warning(f'{sn}: 查询产品信息 失败:{resp}!')
                    return -1, resp
            else:
                logger.error(f'{sn}: 查询产品信息 请求失败！code: {response.status_code}')
                return -2, response.status_code
        except Exception as err:
            logger.warning(f'{sn}: 查询产品信息 失败:{err}!')
            return -3, err

    @check_login
    def download_file(self, mn, work_order_no, process, file_type, file_dir):
        step_name = 'process_file_url'
        process_info = {
            'file_type': int(file_type),
            'process': process,
            'token': self.token
        }
        if mn:
            process_info.update({"mn": mn})
        elif work_order_no:
            process_info.update({'work_order_no': work_order_no})

        # 发送 HTTP GET 请求
        response = requests.get(self.url_base + step_name, params=process_info,stream=True,allow_redirects=True)
        # 检查请求是否成功
        if response.status_code == 200:
            context = requests.get(eval(response.content))
            print("response.content:",response.content)
            #检查响应内容的大小
            if len(context.content) < 1024:  # 小于 1KB
                logger.info(f"Downloaded file is too small: {len(context.content)} bytes")
            else:
                # 将 ZIP 文件保存到本地
                local_zip_path = f'{file_dir}/fw_file_download.zip'

                # 将响应内容以二进模式制写入文件
                with open(local_zip_path,'wb') as file:
                    file.write(context.content)
                logger.info(f"文件已成功下载并保存到 {file_dir}")
                logger.info(f"文件大小: {os.path.getsize(local_zip_path)} 字节")
                time.sleep(2)

                decoded_filename_list = []
                # 使用 zipfile 库打开 ZIP 文件
                with zipfile.ZipFile(local_zip_path, 'r') as zip_ref:
                    # 遍历文件名并提取 .bin 和 .xlsx 文件
                    for file_info in zip_ref.infolist():
                        try:
                            # 尝试使用检测到的编码解码文件名
                            decoded_filename = file_info.filename.encode('cp437').decode('gbk')
                            decoded_filename_list.append(decoded_filename)
                            logger.info(f"解码文件名:{decoded_filename}")
                        except UnicodeDecodeError:
                            logger.info(f"Failed to decode filename: {file_info.filename}")

                        # 构建解压后的文件路径
                        extracted_path = os.path.join(file_dir, decoded_filename)

                        # 确保目标目录存在
                        os.makedirs(os.path.dirname(extracted_path), exist_ok=True)

                        # 提取文件到当前目录
                        if decoded_filename.endswith('.bin') or decoded_filename.endswith('.xlsx'):
                            with zip_ref.open(file_info) as source, open(extracted_path, 'wb') as target:
                                target.write(source.read())
                                time.sleep(2)
                            logger.info(f'Extracted: {decoded_filename}')
                return decoded_filename_list,"固件下载完成"
        else:
            logger.info(f"请求失败，状态码: {response.status_code}")

pcs = PCS()


if __name__ == '__main__':
    print(pcs.login('53710','53710','XXX'))
    print(pcs.get_product_info("151086200064"))
    # print(pcs.start_process('2071927124100006', '测试-终测', False, False))
    #
    # #pcs.cache_material_data('1037866108', 'm1', 'm1', 123)
    # print(pcs.process_test_data('2071927124100006', '测试-终测'))
    # print(pcs.upload_test_data('1037866108', r'D:\TestResult\last_test_CFO0001.xlsx'))
    # print(pcs.finish_process('1037866108', 'True'))
    # print(pcs.posit_process('1037866108', '冷加工-芯片开槽'))
    # print(pcs.process_test_data('1037866108', '冷加工-芯片开槽'))

    # sn = 'OCT4N324100028'
    # status, data = pcs.get_product_info(sn)
    # print(pcs.product_info[sn])


    pass


