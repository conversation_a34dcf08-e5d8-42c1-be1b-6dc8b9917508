import paramiko

def ssh_exec(hostname, port, username, password, command):
    # 1. 创建 SSHClient 实例
    client = paramiko.SSHClient()
    # 2. 自动添加未知的主机密钥（不推荐用于生产环境，可改为 RejectPolicy）
    client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    # 3. 建立连接
    client.connect(hostname=hostname, port=port,
                   username=username, password=password,
                   timeout=10)
    # 4. 执行命令
    stdin, stdout, stderr = client.exec_command(command)
    # 5. 读取结果
    out = stdout.read().decode('utf-8', errors='ignore')
    err = stderr.read().decode('utf-8', errors='ignore')
    # 6. 关闭连接
    client.close()
    return out, err

if __name__ == '__main__':
    host = '*************'
    user = 'admin'
    pwd  = 'password'
    cmd  = 'uname -a'
    out, err = ssh_exec(host, 22, user, pwd, cmd)
    print('STDOUT:', out)
    print('STDERR:', err)
