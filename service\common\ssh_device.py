from typing import List, Dict, Union
import re

from service.ssh.ssh import SshConnection
from service.common.my_logger import logger
from service.common.const import SINGLE_VALUE_KEY_LIST, MULTI_VALUE_KEY_LIST


class SshDevice:
    def __init__(
        self, ssh_connection: SshConnection, oct_slot_list: list[Union[int, str]]
    ):
        self.ssh_connection = ssh_connection
        self.module_info_dict: Dict[Union[int, str], Dict] = {
            module_index: {"online_status": False, "module_index": module_index}
            for module_index in oct_slot_list
        }

    def init_device_coon(self):
        if self.ssh_connection.connection_status == False:
            self.ssh_connection.close()
        self.ssh_connection.connect()
        result_sys = self.ssh_connection.send_command("sys")
        if result_sys == "sys \r\n[ACCELINK]":
            logger.info(
                f"连接ssh成功, ip: {self.ssh_connection.ip}, port: {self.ssh_connection.port}"
            )
        else:
            raise ConnectionError(
                f"连接ssh失败, ip: {self.ssh_connection.ip}, port: {self.ssh_connection.port}"
            )

    def switch_module(self, slot_str: str):
        slot_command = f"slot {slot_str}"
        result_slot: str = self.ssh_connection.send_command(slot_command)
        logger.info(f"切换查询模块: {result_slot}")

    def ssh_send_exit(self):
        self.ssh_connection.send_command("exit")

    def check_module_exists(self):
        for module_index, module_info in self.module_info_dict.items():
            slot_str = self.format_slot_str(module_index)
            self.switch_module(slot_str)
            result_display: str = self.send_display_cfp2_dco_command()
            if "The cfp2dco Module No.1 module is absent!" in result_display:
                module_info.update({"online_status": False})
                raise Exception(f"插槽{slot_str}上按照配置应该有模块,但是通过OCT设备获取到的数据显示该插槽上没有模块")
            elif result_display:
                module_info.update({"online_status": True})
            else:
                self.ssh_send_exit()
                raise ConnectionError(
                    f"设备接受命令失败, 返回值为空字符串, ip: {self.ssh_connection.ip}, port: {self.ssh_connection.port}"
                )
            self.ssh_send_exit()

    def format_slot_str(self, slot_index: Union[int, str]):
        if type(slot_index) is int:
            return f"1/{slot_index}"
        else:
            return f"1/{slot_index}"

    def read_online_display_cfp2_dco_info(self):
        result_dict = {}
        for module_index, module_info in self.module_info_dict.items():
            online_status: bool = module_info["online_status"]
            if online_status:
                result_display = self.read_single_display_cfp2_dco_info(module_index)
                result_dict.update({module_index: result_display})
        return result_dict

    def send_display_cfp2_dco_command(self):
        result_display: str = self.ssh_connection.send_command("display cfp2-dco")
        return result_display

    def read_single_display_cfp2_dco_info(self, module_index: Union[int, str]):
        slot_str = self.format_slot_str(module_index)
        self.switch_module(slot_str)
        result_display = self.send_display_cfp2_dco_command()
        self.ssh_send_exit()
        return result_display

    def trans_result_str_to_key_value_dict(self, result_str: str) -> Dict[str, str]:
        key_list = SINGLE_VALUE_KEY_LIST
        key_str = "|".join(key_list)
        pattern = re.compile(rf"({key_str})\s*:\s*(\S+)")
        matches = pattern.findall(result_str)
        # 将结果转换为字典
        result = {key: value for key, value in matches}
        key_list_multi = MULTI_VALUE_KEY_LIST
        key_str_multi = "|".join(key_list_multi)
        pattern_multi = re.compile(rf"({key_str_multi})\s*:\s*(.*?)\s*(?=\n|$)")
        matches_multi = pattern_multi.findall(result_str)
        result_multi: Dict[str, str] = {key: value for key, value in matches_multi}
        for k, v in result_multi.items():
            values = v.split(" ")
            for value_index, value in enumerate(values):
                result.update({f"{k}[{value_index + 1}]": value})
        return result

    def get_sn_by_result_str(self, result_str: str) -> str:
        pattern = re.compile(r"Vendor SN\s*:\s*(\S+)")
        match = pattern.search(result_str)
        if match:
            result = match.group(1)
            return result
        else:
            raise Exception("无法从字符串中获取到Vendor SN")
