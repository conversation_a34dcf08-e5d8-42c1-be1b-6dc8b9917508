from PySide6.QtWidgets import QApplication, QTableView, QVBoxLayout, QWidget
from PySide6.QtGui import QColor
from PySide6.QtCore import Qt, QAbstractTableModel, QModelIndex

from typing import List, Dict, Tuple


class MyTableModel(QAbstractTableModel):
    def __init__(self, data: List, headers: List[Dict]):
        super().__init__()
        self.ori_data = data  # 确保是列表
        self.headers = headers  # 确保是列表
        self.unique_key_to_row_index_map = {}
        self.red_index = []
        # self.ip_and_mode_to_table_row_index_dict = {}
        # self.sn_and_mode_to_table_row_index_dict = {}
        # self.sn_to_table_row_index_list_dict = defaultdict(list)

    def rowCount(self, parent=None):
        return len(self.ori_data)

    def columnCount(self, parent=None):
        return len(self.headers)

    def data(self, index: QModelIndex, role):
        if role == Qt.ItemDataRole.DisplayRole:
            row = self.ori_data[index.row()]  # 正确访问
            column_key = self.headers[index.column()]["prop"]  # 正确访问
            return row.get(column_key, "")  # 返回值或空字符串
        elif role == Qt.ItemDataRole.BackgroundRole:
            # 为特定行设置背景颜色
            if index.row() in self.red_index:
                return QColor("#FF4C4C")
        return None

    def headerData(self, section, orientation, role):
        if role == Qt.ItemDataRole.DisplayRole:
            if orientation == Qt.Orientation.Horizontal:
                return self.headers[section]["label"]
            elif orientation == Qt.Orientation.Vertical:  # 垂直表头
                return f"{section + 1}"
        return None

    def setData(self, index: QModelIndex, value, role=Qt.ItemDataRole.EditRole):
        """修改单元格数据，并通知 UI"""
        if role == Qt.ItemDataRole.EditRole:
            self.ori_data[index.row()][index.column()] = value
            self.dataChanged.emit(index, index)  # 通知 UI 这个单元格已更新

    def create_single_row(self, row_dict):
        unique_key = (row_dict["slot"], )
        self.ori_data.append(row_dict)
        self.unique_key_to_row_index_map.update({unique_key: len(self.ori_data) - 1})
        self.layoutChanged.emit()

    def update_all_data(self, data: List[Dict]):
        self.ori_data = data
        for row_index, doc in enumerate(data):
            unique_key = (doc["slot"], )
            self.unique_key_to_row_index_map.update({unique_key: row_index})
        self.layoutChanged.emit()

    def update_single_row(self, unique_key: Tuple, update_row_dict: dict):
        row_index = self.unique_key_to_row_index_map[unique_key]
        ori_row_dict: dict = self.ori_data[row_index]
        new_row_dict = ori_row_dict
        new_row_dict.update(update_row_dict)
        self.ori_data[row_index] = new_row_dict
        model_index_start = self.index(row_index, 0)
        model_index_end = self.index(row_index, len(self.headers) - 1)
        self.dataChanged.emit(model_index_start, model_index_end)

    def change_row_color(self, unique_key: Tuple):
        row_index = self.unique_key_to_row_index_map[unique_key]
        self.red_index.append(row_index)

    def reinit(self):
        self.ori_data = []
        self.unique_key_to_row_index_map = {}
        self.red_index = []

    # @Slot(list)
    # def update_table(self, data:List[Dict]):
    #     for doc in data:
    #         if doc["type"] == "add":
    #             self.ori_data.append(doc)
    #             self.ip_and_mode_to_table_row_index_dict.update({
    #                 (doc["ip"], doc["模式名称"]): len(self.ori_data) - 1
    #             })
    #         elif doc["type"] == "first_update":
    #             row_index = self.ip_and_mode_to_table_row_index_dict.get((doc["ip"], doc["模式名称"]))
    #             if row_index != None:
    #                 self.ori_data[row_index].update(doc)
    #                 self.sn_and_mode_to_table_row_index_dict.update({
    #                     (doc["SN"], doc["模式名称"]): row_index
    #                 })
    #                 self.sn_to_table_row_index_list_dict[doc["SN"]].append(row_index)
    #         elif doc["type"] == "update":
    #             row_index = self.sn_and_mode_to_table_row_index_dict.get((doc["SN"], doc["模式名称"]))
    #             if row_index != None:
    #                 self.ori_data[row_index].update(doc)
    #                 if doc.get("测试是否通过") == "否":
    #                     self.red_index.append(row_index)
    #         elif doc["type"] == "finish":
    #             row_index = self.sn_and_mode_to_table_row_index_dict.get((doc["SN"], doc["模式名称"]))
    #             if row_index != None:
    #                 self.ori_data[row_index].update(doc)
    #                 if doc.get("测试是否通过") == "否":
    #                     self.red_index.append(row_index)
    #         elif doc["type"] == FAIL_ALL:
    #             row_index_list = self.sn_to_table_row_index_list_dict[doc["SN"]]
    #             for row_index in row_index_list:
    #                 self.ori_data[row_index].update({"测试是否通过": "否"})
    #                 self.red_index.append(row_index)
    #         elif doc["type"] == FAIL_ONE_MODULE:
    #             row_index_list = self.sn_to_table_row_index_list_dict[doc["SN"]]
    #             for row_index in row_index_list:
    #                 self.ori_data[row_index].update({"测试是否通过": "否"})
    #                 self.red_index.append(row_index)
    #     self.layoutChanged.emit()
